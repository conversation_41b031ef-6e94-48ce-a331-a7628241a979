<template>
  <div class="videoMgtBox">
    <div class="videoMgtLayout">
      <!-- 筛选栏 -->
      <div class="filterBox">
        <!-- 分组下拉筛选 -->
        <div class="operationType filterItem">
          <span>{{ $t("group") }}</span>
          <el-select
            style="width: 400px"
            v-model="operationType"
            :placeholder="$t('pleaseSelect')"
            @change="onChangeSelect"
          >
            <el-option
              v-for="item in operationTypeRange"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>

        <!-- 输入框 -->
        <div class="operationType filterItem">
          <span>{{ $t("device") }}</span>
          <el-input
            v-model="searchText"
            :placeholder="$t('deviceSearchTip')"
            maxlength="100"
            style="width: 200px"
            @keyup.enter.native="search()"
          ></el-input>
        </div>

        <!-- 时间选择 -->
        <div class="operationType filterItem">
          <span>{{ $t("time") }}</span>
          <el-date-picker
            v-model="operationTime"
            type="daterange"
            range-separator="~"
            :start-placeholder="$t('startDate')"
            :end-placeholder="$t('endDate')"
          >
          </el-date-picker>
        </div>

        <!-- 搜索按钮 -->
        <el-button
          type="primary"
          class="tipSubmitBtn"
          icon="el-icon-search"
          @click="search()"
        >
          {{ $t("search") }}
        </el-button>
      </div>
      <!-- 筛选栏 end -->

      <!-- 操作栏 -->
      <div class="operateBox">
        <!-- 全选 -->
        <el-checkbox
          class="selectAll"
          v-model="checkedAll"
          :indeterminate="isHasSomeChecked"
          @change="handleAllChecked"
          >{{ $t("allselect") }}</el-checkbox
        >
        <!-- 下载 -->
        <el-button class="tipSubmitBtn" @click="delBulkUser()">{{
          $t("download")
        }}</el-button>
        <!-- 删除 -->
        <el-button class="tipDeleteBtn" @click="addUser()">{{
          $t("delete")
        }}</el-button>
      </div>
      <!-- 操作栏 end -->

      <!-- 列表 -->
      <div class="rowListBox">
        <div class="rowItem" v-for="item in rowList" :key="item.name">
          <el-checkbox
            v-model="item.checked"
            @change="handleRowChecked"
          ></el-checkbox>
          <div class="rowImageBox" @click="handlePlayVideo(item)">
            <img class="rowImage" :src="item.imageUrl" />
            <img
              class="rowPlayIcon"
              src="../../../src/assets/img/playIcon.png"
              alt=""
            />
          </div>
          <div class="rowInfo">
            <div class="rowDesc">
              <span class="rowName">{{ item.name }}</span>
              <span class="rowGroup">{{ item.group }}</span>
            </div>
            <div class="rowTime">{{ item.timeText }}</div>
          </div>
          <div style="flex: 1"></div>
          <div class="rowOperate">
            <el-button type="text" class="rowOperateBtn download">{{
              $t("download")
            }}</el-button
            ><span class="rowOperateSplit">|</span
            ><el-button type="text" class="rowOperateBtn delete">{{
              $t("delete")
            }}</el-button>
          </div>
        </div>
      </div>
      <!-- 列表 end -->

      <!-- 分页 -->
      <div class="paginationBox">
        <pagination
          v-show="total > 10"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          class="text-right mar_top_0"
          @pagination="handlePaginationChange"
        />
      </div>
      <!-- 分页 end -->
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { handleColumnShow } from '@/utils/index'
export default {
  name: "VideoMgt",
  components: {
    Pagination,
  },
  data () {
    return {
      operationTypeRange: [
        {
          value: "1",
          label: this.$t("all"),
        },
        {
          value: "2",
          label: this.$t("online"),
        },
        {
          value: "3",
          label: this.$t("offline"),
        },
      ],
      operationType: "1",
      searchText: "",

      allChecked: false,
      isHasSomeChecked: false,
      checkedList: [],

      rowList: [{
        checked: false,
        name: '智能摄像机asdfasdfsdaf ',
        group: '分组1',
        timeText: '2025-06-11 12:00 ~ 2025-06-11 12:01',
        imageUrl: 'http://yf.cylan.com.cn:82/web/prm/testimage.jpg',
        videoUrl: 'http://yf.cylan.com.cn:82/web/prm/testvideo.mp4'
      }, {
        checked: false,
        name: '智能摄像机2',
        group: '分组1',
        timeText: '2025-06-11 12:00 ~ 2025-06-11 12:01',
        imageUrl: 'http://yf.cylan.com.cn:82/web/prm/testimage.jpg',
        videoUrl: 'http://yf.cylan.com.cn:82/web/prm/testvideo.mp4'
      }],

      total: 22,
      list_loading: false,
      listQuery: {
        page: 1,
        limit: 20
      },
    }
  },
  methods: {
    onChangeSelect () { },
    search () { },
    handleAllChecked (val) {
      this.checkedAll = val
    },
    handleRowChecked (val) {
      let hasChecked = this.rowList.some(item => item.checked)
      let hasUnChecked = this.rowList.some(item => !item.checked)
      this.isHasSomeChecked = hasChecked && hasUnChecked
      this.checkedList = this.rowList.filter(item => item.checked)
      this.allChecked = hasChecked && !hasUnChecked
    },
    handlePaginationChange (val) {
      console.log(val)
    },
    handlePlayVideo (item) {
      console.log(item)
    }
  },
  computed: {
    checkedAll: {
      get () {
        return this.allChecked
      },
      set (val) {
        this.allChecked = val
        this.rowList.map(item => {
          item.checked = val
        })
        this.isHasSomeChecked = false
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.videoMgtBox {
  height: calc(100vh - 75px);
  background: var(--color-neutral-700);
  overflow-y: scroll;
}
.videoMgtLayout {
  max-width: 1260px;
  box-sizing: border-box;
  margin: auto;
  padding: 24px 20px 24px;
}
.operateBox {
  margin-bottom: 30px;

  .tipSubmitBtn {
    margin-left: 30px;
  }
  .tipCancelBtn {
    margin-left: 20px;
  }
  .el-button {
    padding: 0 31px;
    font-size: var(--font-size-small);
  }
}
.filterBox {
  margin-bottom: 30px;
}
.rowListBox {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-bottom: 2px;
  .rowItem {
    display: flex;
    align-items: center;
    background-color: var(--color-neutral-600);
    padding: 0 40px 0 20px;
    min-height: 100px;
    .rowImageBox {
      width: 110px;
      height: 60px;
      margin-left: 20px;
      background-color: var(--color-neutral-400);
      border-radius: 6px;
      overflow: hidden;
      flex-shrink: 0;
      position: relative;
      transition: all 0.2s ease;
      &:hover {
        opacity: 0.8;
        transform: scale(1.1);
        cursor: pointer;
      }
      .rowImage {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .rowPlayIcon {
        width: 30px;
        height: 30px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .rowInfo {
      margin-left: 24px;
      .rowDesc {
        margin-bottom: 28px;
        .rowName {
          margin-right: 67px;
          font-size: var(--font-size-regular);
          font-weight: bold;
        }
        .rowGroup {
          font-size: var(--font-size-regular);
        }
      }
      .rowTime {
        color: var(--color-neutral-200);
      }
    }
    .rowOperate {
      .rowOperateSplit {
        color: var(--color-neutral-400);
        margin-left: 20px;
        margin-right: 20px;
        user-select: none;
      }
      .download {
        color: #86c8ff;
        &:hover {
          opacity: 0.8;
        }
      }
      .delete {
        color: var(--color-negative-400);
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}

.paginationBox {
  display: flex;
  justify-content: flex-end;
  background-color: var(--color-neutral-600);
}
</style>
