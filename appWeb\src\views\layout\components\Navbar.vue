<template>
  <div class="navbar">
    <div class="infoBox">
      <div class="platInfo">
        <img class="platLogo" src="../../../assets/img/home_logo.png" />
        <div class="platNameBox">
          <span class="platName">{{ $t("mgtPlat") }}</span>
          <span class="platNameTip">{{ $t("mgtPlatTip") }}</span>
        </div>
      </div>
      <div class="menuCard">
        <div
          :class="{ selectedSubMenu: selected_menu_flag === 'home' }"
          class="subMenu"
          @click="toggleMenu('home')"
        >
          {{ $t("home") }}
        </div>
        <div
          :class="{ selectedSubMenu: selected_menu_flag === 'device' }"
          class="subMenu"
          @click="toggleMenu('device')"
        >
          {{ $t("deviceManagement") }}
        </div>
        <div
          v-if="isSuperAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'user' }"
          class="subMenu"
          @click="toggleMenu('user')"
        >
          {{ $t("userManagement") }}
        </div>
        <div
          :class="{ selectedSubMenu: selected_menu_flag === 'system' }"
          class="subMenu"
          @click="toggleMenu('system')"
        >
          {{ $t("systemSetting") }}
        </div>
        <div
          :class="{ selectedSubMenu: selected_menu_flag === 'video_management' }"
          class="subMenu"
          @click="toggleMenu('video_management')"
        >
          {{ $t("videomanagement") }}
        </div>
        <div
          v-if="isSuperAdmin"
          :class="{ selectedSubMenu: selected_menu_flag === 'logs' }"
          class="subMenu"
          @click="toggleMenu('logs')"
        >
          {{ $t("operationLogs") }}
        </div>
      </div>

      <div class="navbar__right">
        <lang-select
          class="set-language"
          @handleSetLanguage="handleSetLanguage"
        />
        <span class="userName">{{ username }}</span>
        <div class="logOut pointer" @click="logoutDialog">
          {{ $t("quitTitle") }}
        </div>
      </div>
    </div>
    <tip-dialog
      ref="tipDialog"
      :title="dialogTitle"
      :tip="dialogTip"
      :request="tipRequest"
      :params="tipParams"
      @handleTip="backTip"
    />
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'
import { getLanguage } from '@/utils/auth'
import TipDialog from '@/components/TipDialog'
export default {
  components: {
    LangSelect,
    TipDialog
  },
  inject: ['reload'],
  data () {
    return {
      language: getLanguage(),
      dialogTitle: '',
      dialogTip: '',
      tipRequest: '',
      tipParams: {},
      selected_menu_flag: 'home',

      isSuperAdmin: false,
      username: sessionStorage.account_alias ? sessionStorage.account_alias : sessionStorage.username
    }
  },
  computed: {
  },
  created () {
    this.selected_menu_flag = this.$route.name
    this.isSuperAdmin = sessionStorage.account_type == '1'
    this.$bus.$on('refreshUserInfo', response => {
      this.username = response
    })
  },
  methods: {
    handleSetLanguage (val) {
      this.language = val
    },
    toggleMenu (flag) {
      this.selected_menu_flag = flag
      this.$router.push({ path: flag })
    },
    logoutDialog () {
      this.dialogTitle = this.$t('quitTitle')
      this.dialogTip = this.$t('quitTip')
      this.tipRequest = 'cli_logout'
      this.tipParams = {}
      this.$refs.tipDialog.show('logout')
    },
    backTip (val) {
      // if(val === 'logout'){
      //   this.$store.dispatch('FedLogOut')
      //   location.reload()
      // }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.navbar {
  height: 70px;
  width: 100%;
  min-width: 1100px;
  background: var(--color-neutral-600);
  display: flex;
  align-items: center;
  .infoBox {
    width: 100%;
    padding: 0 20px;
    height: 66px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  }
  .platInfo {
    display: flex;
    margin-right: 40px;
    .platLogo {
      width: 50px;
      height: 50px;
      margin-right: 10px;
    }
    .platNameBox {
      display: flex;
      justify-content: center;
      flex-direction: column;
      .platName {
        font-size: var(--font-size-large);
        font-weight: bolder;
      }
      .platNameTip {
        font-size: var(--font-size-small);
      }
    }
    .set-language {
      font-size: 18px;
      height: 40px;
      line-height: 40px;
    }
  }
  .menuCard {
    display: flex;
    font-size: var(--font-size-regular);
    color: #8e959d;
    .subMenu {
      display: flex;
      padding: 0 16px;
      align-items: center;
      height: 66px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        background-color: var(--color-neutral-hover);
      }
    }
    .selectedSubMenu {
      font-weight: bolder;
      color: var(--color-primary);
    }
  }
  .navbar__right {
    display: flex;
    gap: 20px;
    .userName {
      color: var(--color-neutral-200);
    }
    .logOut {
      color: var(--color-neutral-200);
      &:hover {
        color: var(--color-primary);
      }
    }
  }
}
</style>
